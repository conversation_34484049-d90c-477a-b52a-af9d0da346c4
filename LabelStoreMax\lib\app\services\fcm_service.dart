import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/events/stock_notification_event.dart';

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  FirebaseMessaging? _messaging;

  /// Initialize FCM service
  Future<void> initialize() async {
    try {
      _messaging = FirebaseMessaging.instance;
      
      // Request permission for notifications
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('FCM: User granted permission');
        await _getAndStoreToken();
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        print('FCM: User granted provisional permission');
        await _getAndStoreToken();
      } else {
        print('FCM: User declined or has not accepted permission');
      }

      // Listen for token refresh
      _messaging!.onTokenRefresh.listen((String token) {
        print('FCM: Token refreshed: $token');
        _storeToken(token);
      });

    } catch (e) {
      print('FCM: Error initializing Firebase Messaging: $e');
    }
  }

  /// Get and store FCM token
  Future<void> _getAndStoreToken() async {
    try {
      String? token = await _messaging?.getToken();
      if (token != null) {
        print('FCM: Token obtained: $token');
        await _storeToken(token);
      } else {
        print('FCM: Failed to get token');
      }
    } catch (e) {
      print('FCM: Error getting token: $e');
    }
  }

  /// Store FCM token in local storage
  Future<void> _storeToken(String token) async {
    try {
      await NyStorage.save('fcm_token', token);
      print('FCM: Token stored successfully');
    } catch (e) {
      print('FCM: Error storing token: $e');
    }
  }

  /// Get stored FCM token
  Future<String?> getStoredToken() async {
    try {
      return await NyStorage.read('fcm_token');
    } catch (e) {
      print('FCM: Error reading stored token: $e');
      return null;
    }
  }

  /// Get current FCM token
  Future<String?> getCurrentToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      print('FCM: Error getting current token: $e');
      return null;
    }
  }

  /// Check if FCM is available and initialized
  bool get isInitialized => _messaging != null;

  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging?.subscribeToTopic(topic);
      print('FCM: Subscribed to topic: $topic');
    } catch (e) {
      print('FCM: Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging?.unsubscribeFromTopic(topic);
      print('FCM: Unsubscribed from topic: $topic');
    } catch (e) {
      print('FCM: Error unsubscribing from topic $topic: $e');
    }
  }

  /// Handle stock notification specifically
  Future<void> handleStockNotification(RemoteMessage message) async {
    try {
      // Check if this is a stock notification
      if (message.data.containsKey('stock_level') &&
          message.data.containsKey('product_id')) {

        String stockLevel = message.data['stock_level'] ?? '0';
        int stock = int.tryParse(stockLevel) ?? 0;

        // Only process low stock notifications
        if (stock <= 5) {
          print('FCM: Processing low stock notification for product ${message.data['product_id']} (Stock: $stock)');

          // Trigger the stock notification event
          event<StockNotificationEvent>(data: {"RemoteMessage": message});
        }
      }
    } catch (e) {
      print('FCM: Error handling stock notification: $e');
    }
  }
}
