//  Velvete Store
//
//  Created by Augment Agent.
//  2025, Velvete Store. All rights reserved.
//

import 'package:firebase_messaging/firebase_messaging.dart';
import '/resources/pages/product_detail_page.dart';
import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';

class StockNotificationEvent implements NyEvent {
  @override
  final listeners = {
    DefaultListener: DefaultListener(),
  };
}

class DefaultListener extends NyListener {
  @override
  handle(dynamic event) async {
    RemoteMessage message = event['RemoteMessage'];

    await _handleLowStockNotification(message);
  }

  /// Handle low stock notification
  _handleLowStockNotification(RemoteMessage message) async {
    // Check if this is a low stock notification
    if (!message.data.containsKey('stock_level') || 
        !message.data.containsKey('product_id')) {
      return;
    }

    String stockLevel = message.data['stock_level'] ?? '0';
    String productId = message.data['product_id'] ?? '';
    String productName = message.data['product_name'] ?? 'منتج';
    
    // Parse stock level
    int stock = int.tryParse(stockLevel) ?? 0;
    
    // Only handle low stock notifications (stock <= 5)
    if (stock > 5) {
      return;
    }

    // Create notification title and message
    String title = 'تنبيه مخزون منخفض';
    String body = 'المنتج "$productName" أوشك على النفاد! المتبقي: $stock قطع فقط';
    
    // Add notification to local storage
    await NyNotification.addNotification(
      title, 
      body, 
      meta: {
        'type': 'low_stock',
        'product_id': productId,
        'product_name': productName,
        'stock_level': stockLevel,
        'timestamp': DateTime.now().toIso8601String(),
      }
    );

    // Update notification icon state
    updateState(NotificationIcon.state);
    
    // If user taps notification, navigate to product detail
    if (message.data.containsKey('action') && 
        message.data['action'] == 'view_product') {
      try {
        int prodId = int.parse(productId);
        routeTo(ProductDetailPage.path, data: prodId);
      } catch (e) {
        print('Error navigating to product: $e');
      }
    }

    print('📦 Low stock notification processed for product: $productName (Stock: $stock)');
  }
}
