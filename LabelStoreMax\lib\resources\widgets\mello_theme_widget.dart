// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/material.dart';
import 'dart:ui';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/browse_category_page.dart';
import '/resources/pages/home_search_page.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/widgets/top_nav_widget.dart';
import 'package:flutter_swiper_view/flutter_swiper_view.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import 'package:carousel_slider/carousel_slider.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/home_drawer_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/bottom_nav_item.dart';

import '/resources/pages/cart_page.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/resources/pages/categories_page.dart';
import '/resources/pages/settings_page.dart';
import '/resources/themes/styles/design_constants.dart';
import '/bootstrap/extensions.dart';


class MelloThemeWidget extends StatefulWidget {
  const MelloThemeWidget({super.key, this.appConfig});
  final dynamic appConfig; // Temporarily dynamic to avoid breaking changes

  @override
  createState() => _MelloThemeWidgetState();
}

class _MelloThemeWidgetState extends NyState<MelloThemeWidget> {
  List<MyProductCategory> _categories = [];
  Widget? activeWidget;
  int _currentIndex = 0;
  List<BottomNavItem> allNavWidgets = [];

  @override
  get init => () async {
        await _fetchCategories();
        await _loadTabs();
      };

  _fetchCategories() async {
    try {
      _categories = await WooCommerceService().getProductCategories(
        parent: 0,
        perPage: 50,
        hideEmpty: true,
      );
      // Sort by name for now (can be customized later)
      _categories.sort((category1, category2) =>
          category1.name.compareTo(category2.name));
    } catch (e) {
      NyLogger.error("Error fetching categories: $e");
      _categories = [];
    }
  }

  _loadTabs() async {
    allNavWidgets = await bottomNavWidgets();
    if (allNavWidgets.isNotEmpty) {
      activeWidget = allNavWidgets[0].tabWidget;
    }
    if (mounted) {
      setState(() {});
    }
  }

  Future<List<MyProduct>> _getProducts() async {
    try {
      return await WooCommerceService().getProducts(
        page: 1,
        perPage: 50,
        status: WooFilterStatus.publish,
        stockStatus: WooProductStockStatus.instock,
      );
    } catch (e) {
      NyLogger.error("Error fetching products: $e");
      return <MyProduct>[];
    }
  }

  Future<List<MyProduct>> _getNewArrivals() async {
    try {
      return await WooCommerceService().getProducts(
        page: 1,
        perPage: 8, // Limit to 8 new arrivals for horizontal scroll
        orderBy: WooSortOrderBy.date,
        order: WooSortOrder.desc,
        status: WooFilterStatus.publish,
        stockStatus: WooProductStockStatus.instock,
      );
    } catch (e) {
      NyLogger.error("Error fetching new arrivals: $e");
      return <MyProduct>[];
    }
  }

  _changeMainWidget(int currentIndex, List<BottomNavItem> allNavWidgets) {
    // Smooth page transition with elegant animation
    if (_currentIndex != currentIndex) {
      setState(() {
        _currentIndex = currentIndex;
        activeWidget = _buildAnimatedPageTransition(
          allNavWidgets[currentIndex].tabWidget,
          currentIndex,
        );
      });
    }
  }

  /// Creates smooth page transitions with Hero animations
  Widget _buildAnimatedPageTransition(Widget child, int index) {
    return AnimatedSwitcher(
      duration: DesignConstants.normalAnimation,
      switchInCurve: DesignConstants.elegantCurve,
      switchOutCurve: DesignConstants.elegantCurve,
      transitionBuilder: (Widget child, Animation<double> animation) {
        // Different transition effects for different pages
        switch (index) {
          case 0: // Home - Slide from bottom
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.3),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 1: // Categories - Slide from right
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.3, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 2: // Cart - Scale and fade
            return ScaleTransition(
              scale: Tween<double>(
                begin: 0.8,
                end: 1.0,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          case 3: // Search - Slide from left
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(-0.3, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          default:
            return FadeTransition(
              opacity: animation,
              child: child,
            );
        }
      },
      child: child,
    );
  }

  Widget _buildNewArrivalsSection() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'منتجات جديدة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.headlineSmall?.color,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to all products or new arrivals page
                    showToast(
                      title: "منتجات جديدة",
                      description: "عرض جميع المنتجات الجديدة",
                      style: ToastNotificationStyleType.info,
                    );
                  },
                  child: Text(
                    'عرض الكل',
                    style: TextStyle(
                      color: Color(0xFFB76E79),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Horizontal Products List
          Container(
            height: 280,
            child: FutureBuilder<List<MyProduct>>(
              future: _getNewArrivals(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFFB76E79),
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      'خطأ في تحميل المنتجات الجديدة',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  );
                }

                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات جديدة حالياً',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  );
                }

                List<MyProduct> newArrivals = snapshot.data!;

                return ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  itemCount: newArrivals.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 160,
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      child: ProductItemContainer(
                        product: newArrivals[index],
                        onTap: () => _showProduct(newArrivals[index]),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<List<BottomNavItem>> bottomNavWidgets() async {
    List<BottomNavItem> items = [];

    // Home - الرئيسية (Elegant line art house)
    items.add(
      BottomNavItem(
          id: 1,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.home_rounded, size: DesignConstants.iconMedium),
            label: 'الرئيسية',
          ),
          tabWidget: _buildHomeWidget()),
    );

    // Categories - الفئات (Refined stacked boxes/tiers)
    items.add(
      BottomNavItem(
          id: 2,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.dashboard_rounded, size: DesignConstants.iconMedium),
            label: 'الفئات',
          ),
          tabWidget: CategoriesPage()),
    );

    // Cart - السلة (Elegant shopping bag)
    items.add(BottomNavItem(
      id: 3,
      bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.shopping_bag_outlined, size: DesignConstants.iconMedium),
          activeIcon: Icon(Icons.shopping_bag, size: DesignConstants.iconMedium),
          label: 'السلة'),
      tabWidget: CartPage(),
    ));

    // Search - البحث (Elegant search glass)
    items.add(
      BottomNavItem(
          id: 4,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.search_outlined, size: DesignConstants.iconMedium),
            activeIcon: Icon(Icons.search_rounded, size: DesignConstants.iconMedium),
            label: 'البحث',
          ),
          tabWidget: HomeSearchPage()),
    );

    // Profile/Settings - الملف الشخصي (Elegant user profile)
    items.add(BottomNavItem(
      id: 5,
      bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.person_outline_rounded, size: DesignConstants.iconMedium),
          activeIcon: Icon(Icons.person_rounded, size: DesignConstants.iconMedium),
          label: 'الملف الشخصي'),
      tabWidget: SettingsPage(),
    ));

    return items;
  }

  Widget _buildHomeWidget() {
    return Scaffold(
      drawer: HomeDrawerWidget(),
      appBar: AppBar(
        title: Text(
          'Velvete Store',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(
              Icons.menu,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () {
              // Navigate to notifications page when implemented
              showToast(
                title: "الإشعارات",
                description: "ستتوفر صفحة الإشعارات قريباً",
                style: ToastNotificationStyleType.info,
              );
            },
          ),
          Stack(
            children: [
              IconButton(
                icon: Icon(
                  Icons.shopping_bag_outlined,
                  size: 28,
                  color: Theme.of(context).appBarTheme.iconTheme?.color,
                ),
                onPressed: () {
                  // Navigate to cart
                  _changeMainWidget(3, allNavWidgets); // Cart is at index 3
                },
              ),
              // Red badge for cart items
              FutureBuilder<List<CartLineItem>>(
                future: Cart.getInstance.getCart(),
                builder: (context, snapshot) {
                  int cartCount = snapshot.data?.length ?? 0;
                  if (cartCount == 0) return SizedBox.shrink();

                  return Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '$cartCount',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          IconButton(
            icon: Icon(
              Icons.search,
              size: 28,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () {
              routeTo(HomeSearchPage.path);
            },
          ),
        ],
      ),
      body: SafeAreaWidget(
        child: RefreshIndicator(
          onRefresh: () async {
            // Refresh products data
            setState(() {});
          },
          child: CustomScrollView(
            slivers: [
              // Header content wrapped in SliverToBoxAdapter
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    // Carousel Slider - First thing on homepage
                    Container(
                      height: 350,
                      margin: EdgeInsets.only(bottom: 16),
                      child: CarouselSlider(
                        options: CarouselOptions(
                          height: 350,
                          autoPlay: true,
                          autoPlayInterval: Duration(seconds: 4),
                          autoPlayAnimationDuration: Duration(milliseconds: 800),
                          autoPlayCurve: Curves.easeInOutCubic,
                          enlargeCenterPage: true,
                          enlargeFactor: 0.2,
                          viewportFraction: 0.9,
                          enableInfiniteScroll: true,
                        ),
                        items: [
                          'https://i.imgur.com/oDevWeY.jpeg',
                          'https://i.imgur.com/LZ1dpeC.jpeg',
                          'https://velvete.ly/wp-content/uploads/2025/05/Untitled-design.png',
                        ].map((imageUrl) {
                          return Builder(
                            builder: (BuildContext context) {
                              return Container(
                                width: MediaQuery.of(context).size.width,
                                margin: EdgeInsets.symmetric(horizontal: 5.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 8,
                                      offset: Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(16),
                                  child: CachedImageWidget(
                                    image: imageUrl,
                                    fit: BoxFit.cover,
                                    height: 350,
                                    width: double.infinity,
                                  ),
                                ),
                              );
                            },
                          );
                        }).toList(),
                      ),
                    ),

                    // Top navigation widget
                    TopNavWidget(onPressBrowseCategories: _modalBottomSheetMenu),

                    // Newest Arrivals Section
                    _buildNewestArrivalsSection(),

                    // New Arrivals Section
                    _buildNewArrivalsSection(),

                    // Discount Products Section
                    _buildDiscountProductsSection(),

                    // General Products Section
                    _buildGeneralProductsSection(),

                    // Product Gallery Grid
                    _buildProductGalleryGrid(),

                    // Review Section
                    _buildReviewSection(),
                  ],
                ),
              ),


            ],
          ),
        ),
      ),
    );
  }



  _modalBottomSheetMenu() {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              trans("Categories"),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _categories.length,
                separatorBuilder: (cxt, i) => Divider(),
                itemBuilder: (BuildContext context, int index) => ListTile(
                  title: Text(parseHtmlString(_categories[index].name)),
                  onTap: () {
                    Navigator.pop(context);
                    routeTo(BrowseCategoryPage.path, data: _categories[index]);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: activeWidget ?? AppLoaderWidget(),
      resizeToAvoidBottomInset: false,
      // Debug test button removed - test page was deleted
      bottomNavigationBar: allNavWidgets.isEmpty
          ? AppLoaderWidget()
          : Container(
              decoration: BoxDecoration(
                color: Theme.of(context).bottomNavigationBarTheme.backgroundColor?.withValues(alpha: 0.95),
                boxShadow: DesignConstants.bottomNavShadow,
                // Frosted glass effect
                backgroundBlendMode: BlendMode.overlay,
              ),
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).bottomNavigationBarTheme.backgroundColor?.withValues(alpha: 0.8),
                    ),
              child: BottomNavigationBar(
                backgroundColor: Colors.transparent,
                onTap: (currentIndex) =>
                    _changeMainWidget(currentIndex, allNavWidgets),
                currentIndex: _currentIndex,
                selectedItemColor: context.color.bottomTabBarIconSelected,
                unselectedItemColor: context.color.bottomTabBarIconUnselected,
                type: BottomNavigationBarType.fixed,
                selectedLabelStyle: TextStyle(
                  color: context.color.bottomTabBarLabelSelected,
                  fontWeight: FontWeight.w600,
                  fontSize: DesignConstants.labelMedium,
                ),
                unselectedLabelStyle: TextStyle(
                  color: context.color.bottomTabBarLabelUnselected,
                  fontSize: DesignConstants.labelMedium,
                  fontWeight: FontWeight.w400,
                ),
                showSelectedLabels: true,
                showUnselectedLabels: true,
                elevation: 0, // Remove default elevation, using custom shadow
                items:
                    allNavWidgets.map((e) => e.bottomNavigationBarItem).toList(),
              ),
                  ),
                ),
              ),
            ),
    );
  }

  _showProduct(MyProduct product) {
    routeTo(ProductDetailPage.path, data: product);
  }

  /// Build Newest Arrivals Section
  Widget _buildNewestArrivalsSection() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'أحدث الوصولات',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 280,
            child: FutureBuilder<List<MyProduct>>(
              future: _getProducts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                List<MyProduct> products = snapshot.data?.take(10).toList() ?? [];
                if (products.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات متاحة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                return CarouselSlider.builder(
                  itemCount: products.length,
                  options: CarouselOptions(
                    height: 280,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 4),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.easeInOutCubic,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.2,
                    viewportFraction: 0.7,
                    enableInfiniteScroll: true,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    MyProduct product = products[index];
                    return GestureDetector(
                      onTap: () => _showProduct(product),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 3,
                              child: ClipRRect(
                                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                                child: CachedImageWidget(
                                  image: product.getFirstImage()?.getSafeImageSrc() ?? '',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.name,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      formatStringCurrency(total: product.price),
                                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                        color: Theme.of(context).colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build Discount Products Section
  Widget _buildDiscountProductsSection() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'منتجات مخفضة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 280,
            child: FutureBuilder<List<MyProduct>>(
              future: _getProducts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                List<MyProduct> products = snapshot.data?.where((p) =>
                  p.regularPrice != null && p.regularPrice!.isNotEmpty &&
                  p.salePrice != null && p.salePrice!.isNotEmpty &&
                  p.regularPrice != p.salePrice).take(10).toList() ?? [];

                if (products.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات مخفضة متاحة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                return CarouselSlider.builder(
                  itemCount: products.length,
                  options: CarouselOptions(
                    height: 280,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 4),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.easeInOutCubic,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.2,
                    viewportFraction: 0.7,
                    enableInfiniteScroll: true,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    MyProduct product = products[index];
                    return GestureDetector(
                      onTap: () => _showProduct(product),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                                    child: CachedImageWidget(
                                      image: product.getFirstImage()?.getSafeImageSrc() ?? '',
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                    ),
                                  ),
                                ),
                              Expanded(
                                flex: 2,
                                child: Padding(
                                  padding: EdgeInsets.all(12),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        product.name,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Text(
                                            formatStringCurrency(total: product.salePrice),
                                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                              color: Theme.of(context).colorScheme.primary,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(width: 8),
                                          Text(
                                            formatStringCurrency(total: product.regularPrice),
                                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                              decoration: TextDecoration.lineThrough,
                                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'خصم',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build General Products Section
  Widget _buildGeneralProductsSection() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'تسوق منتجاتنا',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 280,
            child: FutureBuilder<List<MyProduct>>(
              future: _getProducts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                List<MyProduct> products = snapshot.data?.skip(5).take(15).toList() ?? [];
                if (products.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات متاحة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                return CarouselSlider.builder(
                  itemCount: products.length,
                  options: CarouselOptions(
                    height: 280,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 4),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.easeInOutCubic,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.2,
                    viewportFraction: 0.7,
                    enableInfiniteScroll: true,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    MyProduct product = products[index];
                    return GestureDetector(
                      onTap: () => _showProduct(product),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 3,
                              child: ClipRRect(
                                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                                child: CachedImageWidget(
                                  image: product.getFirstImage()?.getSafeImageSrc() ?? '',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.name,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      formatStringCurrency(total: product.price),
                                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                        color: Theme.of(context).colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build Product Gallery Grid - Staggered Grid with Carousel
  Widget _buildProductGalleryGrid() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'تشكيلة متنوعة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 400,
            child: FutureBuilder<List<MyProduct>>(
              future: _getProducts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                List<MyProduct> products = snapshot.data ?? [];
                if (products.isEmpty) {
                  return Center(
                    child: Text(
                      'لا توجد منتجات متاحة',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                // Create sections of products for carousel
                List<List<MyProduct>> productSections = [];
                for (int i = 0; i < products.length; i += 6) {
                  productSections.add(
                    products.skip(i).take(6).toList()
                  );
                }

                return Swiper(
                  itemBuilder: (context, sectionIndex) {
                    List<MyProduct> sectionProducts = productSections[sectionIndex];

                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 8),
                      child: StaggeredGrid.count(
                        crossAxisCount: 4,
                        mainAxisSpacing: 8,
                        crossAxisSpacing: 8,
                        children: sectionProducts.asMap().entries.map((entry) {
                          int index = entry.key;
                          MyProduct product = entry.value;

                          // Define different sizes for staggered effect
                          int crossAxisCellCount = _getGridCrossAxisCount(index);
                          int mainAxisCellCount = _getGridMainAxisCount(index);

                          return StaggeredGridTile.count(
                            crossAxisCellCount: crossAxisCellCount,
                            mainAxisCellCount: mainAxisCellCount,
                            child: GestureDetector(
                              onTap: () => _showProduct(product),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).cardColor,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: IntrinsicHeight(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                    Flexible(
                                      flex: 3,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                                        child: CachedImageWidget(
                                          image: product.getFirstImage()?.getSafeImageSrc() ?? '',
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          height: double.infinity,
                                        ),
                                      ),
                                    ),
                                    Flexible(
                                      flex: 1,
                                      child: Padding(
                                        padding: EdgeInsets.all(8),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              product.name,
                                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            SizedBox(height: 2),
                                            Text(
                                              formatStringCurrency(total: product.price),
                                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                color: Theme.of(context).colorScheme.primary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    );
                  },
                  itemCount: productSections.length,
                  viewportFraction: 0.9,
                  scale: 0.95,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to get cross axis count for staggered grid
  int _getGridCrossAxisCount(int index) {
    switch (index % 6) {
      case 0: return 2; // Large item
      case 1: return 2; // Large item
      case 2: return 1; // Small item
      case 3: return 1; // Small item
      case 4: return 2; // Medium item
      case 5: return 2; // Medium item
      default: return 2;
    }
  }

  /// Helper method to get main axis count for staggered grid
  int _getGridMainAxisCount(int index) {
    switch (index % 6) {
      case 0: return 3; // Tall item
      case 1: return 2; // Medium height
      case 2: return 2; // Medium height
      case 3: return 3; // Tall item
      case 4: return 2; // Medium height
      case 5: return 2; // Medium height
      default: return 2;
    }
  }

  /// Build Review Section
  Widget _buildReviewSection() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'آراء الزباين على المنتجات والخدمات؟',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 16),
              itemCount: 2,
              itemBuilder: (context, index) {
                // Static review data provided by the Master
                final reviews = [
                  {
                    'name': 'حنان علي',
                    'major': 'زبونة خاصة',
                    'review': 'خشّي على موقعهم، تصفّحي وأنتِ مرتاحة في حوشك، وخلال أيام الفستان يوصلك لعند الباب. أنا آخر مرة كنت محتاجة فستان لعزومة عائلية بسيطة ولقيت عندهم حاجة تهبل، لا هي رسمية هلبا ولا هي عادية. الجودة خرافية والأهم إنّي حسّيت روحي واثقة وفخمة فيه. بجد، Velvete حلّ لي مشكلة كبيرة كانت تواجهني كل مرة نطلع فيها. أنصح بيه كل بنت ليبية تدوّر الأناقة والراحة.',
                    'image': 'https://velvete.ly/wp-content/uploads/2025/05/unnamed.jpg',
                    'rating': 5,
                  },
                  {
                    'name': '*********',
                    'major': 'زبونة خاصة',
                    'review': 'بصراحة، Velvet غيّرلي مفهوم اللبس للمناسبات في ليبيا. قبل كنت ندوخ وندور هلبا على حاجة تجي على خاطري، لكن توا خلاص! كل مرة نلقى اللي نبيه وزيادة. آخر فستان خديته منهم لعرس خوي كان تحفة، الخامة والقَصّة ولا أروع، والكل سألني عليه. خدمة الزباين عندهم ممتازة جداً، يساعدوك تختاري وكأنهم فاهمين شن تبّي بالزبط. موقعهم ساهل في التصفح والتوصيل كان سريع وما فيهش أي تعقيدات. من جد، Velvete هو خياري الأول لكل مناسبة.',
                    'image': 'https://velvete.ly/wp-content/uploads/2025/05/164617910d4d76eb309b983149ae9297.jpg',
                    'rating': 5,
                  },
                ];

                final review = reviews[index];
                return Container(
                  width: 320,
                  margin: EdgeInsets.only(right: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundImage: NetworkImage(review['image'] as String),
                            backgroundColor: Colors.grey[300],
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  review['name'] as String,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  review['major'] as String,
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: List.generate(5, (starIndex) {
                              return Icon(
                                starIndex < (review['rating'] as int) ? Icons.star : Icons.star_border,
                                color: Colors.amber,
                                size: 16,
                              );
                            }),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      Expanded(
                        child: Text(
                          review['review'] as String,
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 6,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
