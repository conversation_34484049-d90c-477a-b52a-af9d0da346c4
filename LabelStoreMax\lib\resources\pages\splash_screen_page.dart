//  Velvete Store
//
//  Created by Augment Agent.
//  2025, Velvete Store. All rights reserved.
//

import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/resources/pages/onboarding_page.dart';
import '/resources/pages/home_page.dart';

class SplashScreenPage extends NyStatefulWidget {
  static RouteView path = ("/splash", (_) => SplashScreenPage());

  SplashScreenPage({super.key}) : super(child: () => _SplashScreenPageState());
}

class _SplashScreenPageState extends NyPage<SplashScreenPage> {
  @override
  get init => () async {
    // Wait for splash animation to complete
    await Future.delayed(Duration(seconds: 5));
    
    // Check if user has seen onboarding before
    bool hasSeenOnboarding = await NyStorage.read('has_seen_onboarding') ?? false;
    
    if (!hasSeenOnboarding) {
      // Navigate to onboarding
      routeTo(OnboardingPage.path, navigationType: NavigationType.pushReplace);
    } else {
      // Navigate directly to home
      routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
    }
  };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Lottie splash screen animation from local asset
            Lottie.asset(
              'public/animations/Logo-splash-screen.json',
              width: 300,
              height: 300,
              fit: BoxFit.contain,
              repeat: false,
              animate: true,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to network if local asset fails
                return Lottie.network(
                  'https://velvete.ly/wp-content/uploads/2025/07/Logo-1-splash-screen-big.json',
                  width: 300,
                  height: 300,
                  fit: BoxFit.contain,
                  repeat: false,
                  errorBuilder: (context, error, stackTrace) {
                    // Final fallback to static logo
                    return Container(
                      width: 300,
                      height: 300,
                      decoration: BoxDecoration(
                        color: Color(0xFFB76E79),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          'V',
                          style: TextStyle(
                            fontSize: 120,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            SizedBox(height: 20),
            // App name
            Text(
              'Velvete',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Color(0xFFB76E79),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
