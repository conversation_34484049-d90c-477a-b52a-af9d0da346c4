import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';

class ProductItemContainer extends StatelessWidget {
  const ProductItemContainer({
    super.key,
    this.product,
    this.onTap,
  });

  final MyProduct? product;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    if (product == null) {
      return SizedBox.shrink();
    }

    return _AnimatedProductCard(
      onTap: onTap,
      product: product,
      child: Container(
        margin: EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(DesignConstants.imageRadius),
                child: Stack(
                  children: [
                    Container(
                      color: Colors.transparent,
                      height: double.infinity,
                      width: double.infinity,
                    ),
                    Hero(
                      tag: 'product_image_${product?.id}',
                      child: CachedImageWidget(
                        image: (product?.hasImages() ?? false
                            ? product?.getFirstImage()?.getSafeImageSrc() ?? getEnv("PRODUCT_PLACEHOLDER_IMAGE")
                            : getEnv("PRODUCT_PLACEHOLDER_IMAGE")),
                        fit: BoxFit.contain,
                        height: double.infinity,
                        width: double.infinity,
                        placeholder: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            backgroundColor: Colors.black12,
                            color: Colors.black54,
                          ),
                        ),
                      ),
                    ),
                    if (isProductNew(product))
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            "جديد!",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    if ((product?.isOnSale() ?? false) &&
                        product?.type != "variable")
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(3),
                          decoration: BoxDecoration(
                            color: Colors.white70,
                          ),
                          child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: '',
                              style: Theme.of(context).textTheme.bodyLarge,
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      "${workoutSaleDiscount(salePrice: product?.getSafeSalePrice(), priceBefore: product?.getSafeRegularPrice())}% ${trans("off")}",
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge!
                                      .copyWith(
                                        color: Colors.black,
                                        fontSize: 13,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      product?.name ?? "",
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 15),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AutoSizeText(
                          "${formatStringCurrency(total: product?.getSafePrice())} ",
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(fontWeight: FontWeight.w800),
                          textAlign: TextAlign.left,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if ((product?.isOnSale() ?? false) && product?.type != "variable")
                          RichText(
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            text: TextSpan(children: [
                              TextSpan(
                                text: '${trans("Was")}: ',
                                style:
                                    Theme.of(context).textTheme.bodyLarge!.copyWith(
                                          fontSize: 11,
                                        ),
                              ),
                              TextSpan(
                                text: formatStringCurrency(
                                  total: product?.getSafeRegularPrice(),
                                ),
                                style:
                                    Theme.of(context).textTheme.bodyLarge!.copyWith(
                                          decoration: TextDecoration.lineThrough,
                                          color: Colors.grey,
                                          fontSize: 11,
                                        ),
                              ),
                            ]),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Enhanced Product Card with sophisticated micro-interactions and Hero animations
class _AnimatedProductCard extends StatefulWidget {
  final VoidCallback? onTap;
  final MyProduct? product;
  final Widget child;

  const _AnimatedProductCard({
    required this.onTap,
    required this.product,
    required this.child,
  });

  @override
  State<_AnimatedProductCard> createState() => _AnimatedProductCardState();
}

class _AnimatedProductCardState extends State<_AnimatedProductCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shadowController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();

    // Scale animation for hover/press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.03,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Shadow animation for depth feedback
    _shadowController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shadowController,
      curve: DesignConstants.smoothCurve,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shadowController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _scaleController.forward();
    _shadowController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _resetAnimations();
  }

  void _handleTapCancel() {
    _resetAnimations();
  }

  void _resetAnimations() {
    _scaleController.reverse();
    _shadowController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _scaleAnimation,
          _shadowAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  width: 1.0,
                ),
                boxShadow: DesignConstants.cardShadow.map((shadow) {
                  return shadow.copyWith(
                    blurRadius: shadow.blurRadius * _shadowAnimation.value,
                    offset: shadow.offset * _shadowAnimation.value,
                  );
                }).toList(),
              ),
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}
